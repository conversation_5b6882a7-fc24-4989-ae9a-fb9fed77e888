using MassTransit;
using Pondres.Omnia.Ingestion.Tests.Clients.Internal;
using Pondres.Omnia.Ingestion.Tests.Clients.External;
using Pondres.Omnia.Ingestion.Tests.Fixtures;
using Pondres.Omnia.Ingestion.Tests.Helper;

namespace Pondres.Omnia.Ingestion.Tests.IntegrationTests;

public class BaseIntegrationTest(IntegrationTestFixture fixture)
{
    protected readonly IntegrationTestFixture fixture = fixture;

    protected static string GetCustomer() => Guid.NewGuid().ToString("N").ToUpper();
    protected InternalIngestionApiClient GetInternalClient() => new(fixture.InternalApiApp.Client);
    protected ExternalIngestionApiClient GetExternalClient() => new(fixture.ExternalApiApp.Client);

    protected static Task WaitForAsync(TimeSpan wait, Func<Task> forFunc, CancellationToken cancellationToken = default)
    {
        return TestHelper.WaitForAsync(wait, forFunc, cancellationToken);
    }

    protected static Task<TResult> WaitForAsync<TResult>(TimeSpan wait, Func<Task<TResult>> forFunc, CancellationToken cancellationToken = default)
    {
        return TestHelper.WaitForAsync(wait, forFunc, cancellationToken);
    }

    protected async Task PublishMessageAsync<TMessageType>(TMessageType message) where TMessageType : class
    {
        var bus = fixture.AzureServiceBusApp.ResolveService<IBus>();
        await bus.Publish(message);
    }

    protected IRequestClient<TRequest> CreateRequestClient<TRequest>() where TRequest : class
    {
        var bus = fixture.AzureServiceBusApp.ResolveService<IBus>();
        return bus.CreateRequestClient<TRequest>();
    }
}