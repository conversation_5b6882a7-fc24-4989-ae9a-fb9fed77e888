using Microsoft.AspNetCore.Mvc;
using Pondres.Omnia.Ingestion.Common.Extensions;
using Pondres.Omnia.Ingestion.Common.Handlers;
using Pondres.Omnia.Ingestion.Common.Model;
using Solude.ApiBase.Contracts.Api;
using Solude.ApiBase.Extensions;
using Solude.Packages.Common;
using System.ComponentModel.DataAnnotations;

namespace Pondres.Omnia.Ingestion.Api.External.Controllers;

[Route("submit")]
public class ExternalIngestionController(CreateOrderCommandHandler orderCommandHandler) : ControllerBase
{
    [HttpPost]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResultDto<OrderCreatedResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResultDto))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResultDto))]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(ApiResultDto))]
    public async Task<IActionResult> IngestExternalOrderAsync(
        [FromForm] IFormFile content,
        [FromHeader(Name = "x-customer-reference")][Required] string customerReference,
        [FromHeader(Name = "x-flow")][Required] string flowName,
        [FromHeader(Name = "x-flow-id")] string? flowId,
        [FromHeader(Name = "x-customer")][Required] string customer,
        [FromHeader(Name = "x-category-one")] string categoryOne,
        [FromHeader(Name = "x-category-two")] string categoryTwo,
        [FromHeader(Name = "x-category-three")] string categoryThree)
    {
        var command = new CreateOrderCommand(
            Customer: customer,
            CustomerReference: customerReference,
            FlowName: flowName,
            FlowId: flowId,
            Source: "ExternalApiIngestion",
            Categories: new OrderCreationModelCategories()
            {
                One = categoryOne,
                Two = categoryTwo,
                Three = categoryThree
            });

        using var stream = new MemoryStream();
        await content.CopyToAsync(stream);
        var result = await orderCommandHandler.HandleAsync(command, stream);

        return result.ToActionResult();
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResultDto<OrderCreatedResponse>))]
    [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResultDto))]
    [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResultDto))]
    [ProducesResponseType(StatusCodes.Status409Conflict, Type = typeof(ApiResultDto))]
    public async Task<IActionResult> IngestExternalOrderAsync(
        [FromHeader(Name = "x-customer-reference")][Required] string customerReference,
        [FromHeader(Name = "x-flow")][Required] string flowName,
        [FromHeader(Name = "x-flow-id")] string? flowId,
        [FromHeader(Name = "x-customer")][Required] string customer,
        [FromHeader(Name = "x-category-one")] string categoryOne,
        [FromHeader(Name = "x-category-two")] string categoryTwo,
        [FromHeader(Name = "x-category-three")] string categoryThree)
    {
        var command = new CreateOrderCommand(
            Customer: customer,
            CustomerReference: customerReference,
            FlowName: flowName,
            FlowId: flowId,
            Source: "ExternalApiIngestion",
            Categories: new OrderCreationModelCategories()
            {
                One = categoryOne,
                Two = categoryTwo,
                Three = categoryThree
            });

        var result = await orderCommandHandler.HandleAsync(command, Request.Body);

        return result.ToActionResult();
    }
}